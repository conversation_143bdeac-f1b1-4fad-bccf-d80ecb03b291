"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { useUserTrips } from "@/lib/domains/trip/trip.hooks"
import { useUserSquads } from "@/lib/domains/squad/squad.hooks"
import { MapPinIcon, Users, Star } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { PageLoading } from "@/components/page-loading"
import { ValidatedImage } from "@/components/validated-image"
import { useValidatedTripImageUrl } from "@/lib/hooks/use-validated-image-url"

// Trip Card Component with validation
function TripCardWithValidation({
  trip,
  squads,
  formatDate,
}: {
  trip: any
  squads: any[]
  formatDate: (date: any) => string
}) {
  const { imageUrl, isValidating, handleImageError } = useValidatedTripImageUrl(trip, "400x200")
  const isCompleted = trip.status === "completed"
  const hasReviewData = trip.reviewAggregate && trip.reviewAggregate.totalReviews > 0
  const needsReview = isCompleted && !trip.userHasReviewed

  const renderStarRating = (rating: number, totalReviews: number) => {
    return (
      <div className="flex items-center gap-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <Star
              key={star}
              className={`h-4 w-4 ${
                star <= rating ? "text-yellow-500 fill-current" : "text-gray-300"
              }`}
            />
          ))}
        </div>
        <span className="text-sm text-muted-foreground">({totalReviews})</span>
      </div>
    )
  }

  return (
    <Card className="overflow-hidden flex flex-col h-full hover:shadow-md transition-shadow">
      <div className="aspect-video relative overflow-hidden">
        <ValidatedImage
          src={imageUrl}
          alt={trip.destination || trip.name}
          aspectRatio="video"
          className={`rounded-t-lg transition-opacity duration-300 ${
            isValidating ? "opacity-75" : "opacity-100"
          }`}
          onError={handleImageError}
          fallbackSrc="/placeholder.svg?height=200&width=400"
          priority
        />
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <h3 className="text-white font-bold">{trip.name}</h3>
          <p className="text-white/80 text-sm">
            {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
          </p>
        </div>
      </div>
      <CardContent className="pt-4 flex-grow">
        <div className="flex justify-between items-center mb-2">
          <Badge>
            {trip.squadId
              ? squads.find((s) => s.id === trip.squadId)?.name || "Squad Trip"
              : "Personal Trip"}
          </Badge>
          {trip.attendees && (
            <div className="text-sm text-muted-foreground flex items-center">
              <Users className="h-3 w-3 mr-1" /> {trip.attendees.length || 0}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <MapPinIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{trip.destination || "Location TBD"}</span>
          </div>
          <p className="line-clamp-2 text-sm text-muted-foreground mt-2">
            {trip.description || "No description provided"}
          </p>

          {/* Review Information for Completed Trips */}
          {isCompleted && (
            <div className="pt-2 border-t">
              {hasReviewData ? (
                <div className="flex items-center justify-between">
                  {renderStarRating(
                    trip.reviewAggregate!.averageRating,
                    trip.reviewAggregate!.totalReviews
                  )}
                  {needsReview && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                      Review Needed
                    </Badge>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">No reviews yet</span>
                  {needsReview && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                      Review Needed
                    </Badge>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-0 mt-auto">
        <Button asChild className="w-full">
          <Link href={`/trips/${trip.id}`}>View Trip</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { HelpCircle } from "lucide-react"
import { TripReviewService } from "@/lib/domains/trip-review/trip-review.service"
import { TripReviewAggregate } from "@/lib/domains/trip-review/trip-review.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { Trip } from "@/lib/domains/trip/trip.types"

interface TripWithReviewData extends Trip {
  reviewAggregate?: TripReviewAggregate
  userHasReviewed?: boolean
}

export default function TripsPage() {
  const router = useRouter()
  const user = useUser()

  // Use domain hooks for data fetching with built-in error handling
  const { trips, loading: tripsLoading } = useUserTrips()
  const { squads, loading: squadsLoading } = useUserSquads()

  // State for trips with review data
  const [tripsWithReviews, setTripsWithReviews] = useState<TripWithReviewData[]>([])
  const [reviewsLoading, setReviewsLoading] = useState(true)

  // Load review data for completed trips
  useEffect(() => {
    const loadReviewData = async () => {
      if (!trips.length || !user) {
        setTripsWithReviews(trips)
        setReviewsLoading(false)
        return
      }

      try {
        setReviewsLoading(true)

        // Filter only completed trips to reduce unnecessary API calls
        const completedTrips = trips.filter((trip) => trip.status === "completed")
        const nonCompletedTrips = trips.filter((trip) => trip.status !== "completed")

        // Process completed trips with review data
        const completedTripsWithReviews = await Promise.all(
          completedTrips.map(async (trip) => {
            try {
              const [reviewAggregate, userReview] = await Promise.all([
                TripReviewService.getTripReviewAggregate(trip.id),
                TripReviewService.getUserReview(trip.id, user.uid),
              ])

              return {
                ...trip,
                reviewAggregate,
                userHasReviewed: userReview !== null,
              }
            } catch (error) {
              console.error(`Error loading review data for trip ${trip.id}:`, error)
              return { ...trip, reviewAggregate: undefined, userHasReviewed: false }
            }
          })
        )

        // Combine completed trips with review data and non-completed trips without review data
        const allTripsWithReviewData = [
          ...completedTripsWithReviews,
          ...nonCompletedTrips.map((trip) => ({
            ...trip,
            reviewAggregate: undefined,
            userHasReviewed: false,
          })),
        ]

        // Sort by original order (by date)
        const sortedTrips = allTripsWithReviewData.sort((a, b) => {
          const aIndex = trips.findIndex((trip) => trip.id === a.id)
          const bIndex = trips.findIndex((trip) => trip.id === b.id)
          return aIndex - bIndex
        })

        setTripsWithReviews(sortedTrips)
      } catch (error) {
        console.error("Error loading review data:", error)
        setTripsWithReviews(trips)
      } finally {
        setReviewsLoading(false)
      }
    }

    loadReviewData()
  }, [trips, user])

  // Combined loading state
  const loading = tripsLoading || squadsLoading || reviewsLoading

  const formatDate = (date: Date | any) => {
    if (!date) return "TBD"
    const dateObj = date.toDate ? date.toDate() : new Date(date)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(dateObj)
  }

  if (loading) {
    return <PageLoading message="Loading trips..." />
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">My Trips</h1>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    aria-label="Squad information"
                  >
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-3" align="start">
                  <p className="text-sm">
                    Start planning your next trip! Trips can range from date nights to summer
                    vacations, just add the preferred Squad to your trip!
                  </p>
                </PopoverContent>
              </Popover>
            </div>
            <Button onClick={() => router.push("/trips/create")}>Plan New Trip</Button>
          </div>

          {tripsWithReviews.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-2xl font-semibold mb-4">No trips yet</h2>
              <p className="text-muted-foreground mb-6">Plan your first trip with your squad</p>
              <Button onClick={() => router.push("/trips/create")}>Plan New Trip</Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tripsWithReviews.map((trip) => (
                <TripCardWithValidation
                  key={trip.id}
                  trip={trip}
                  squads={squads}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
