import { NextRequest, NextResponse } from "next/server"

/**
 * Server-side image URL validation endpoint
 * This endpoint validates if an image URL is accessible without CORS issues
 */
export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json()

    if (!imageUrl || typeof imageUrl !== 'string') {
      return NextResponse.json(
        { error: 'Invalid imageUrl parameter' },
        { status: 400 }
      )
    }

    // Validate the image URL server-side using HEAD request
    const response = await fetch(imageUrl, { 
      method: 'HEAD',
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    // Check if the response is successful
    const isAccessible = response.ok && !response.status.toString().startsWith('4')

    return NextResponse.json({
      isAccessible,
      status: response.status,
      statusText: response.statusText,
    })

  } catch (error) {
    console.error('Error validating image URL:', error)
    
    // If it's a timeout or network error, consider the image inaccessible
    return NextResponse.json({
      isAccessible: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
