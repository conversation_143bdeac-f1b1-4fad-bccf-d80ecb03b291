"use client"

import { useState, useEffect, useCallback } from "react"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"
import { getSuggestionImageUrlWithValidation } from "@/lib/utils/trip-image-utils"

interface UseEnhancedSuggestionImageReturn {
  imageUrl: string
  isLoading: boolean
  hasError: boolean
  retryCount: number
  handleImageError: () => void
}

/**
 * Enhanced hook for loading AI trip suggestion images with comprehensive fallback flow
 * Implements: GooglePlaceImage -> validate old image -> fetch new GooglePlaceImage -> generate URL
 */
export function useEnhancedSuggestionImage(
  suggestion: CachedTripSuggestion,
  placeholderSize: string = "300x150"
): UseEnhancedSuggestionImageReturn {
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  const loadImage = useCallback(async () => {
    if (!suggestion) return

    setIsLoading(true)
    setHasError(false)

    try {
      // For welcome page suggestions, be more aggressive about refreshing images
      // First try to get a fresh URL from GooglePlaceImage data if available
      if (suggestion.googlePlaceImage?.photoReference && suggestion.googlePlaceImage?.placeId) {
        const { getSuggestionImageUrlAsync } = await import("@/lib/utils/trip-image-utils")
        const freshUrl = await getSuggestionImageUrlAsync(suggestion, placeholderSize)
        if (freshUrl && !freshUrl.includes("placeholder.svg")) {
          setImageUrl(freshUrl)
          return
        }
      }

      // Fallback to the validation flow
      const url = await getSuggestionImageUrlWithValidation(suggestion, placeholderSize)
      setImageUrl(url)
    } catch (error) {
      console.error("Error loading enhanced suggestion image:", error)
      setHasError(true)
      // Fallback to placeholder
      setImageUrl(`/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`)
    } finally {
      setIsLoading(false)
    }
  }, [suggestion, placeholderSize])

  const handleImageError = useCallback(async () => {
    if (retryCount < 2) {
      // Allow up to 2 retries
      setRetryCount((prev) => prev + 1)

      // On error, try to force refresh the image using GooglePlaceImage data
      if (suggestion?.googlePlaceImage?.photoReference && suggestion?.googlePlaceImage?.placeId) {
        try {
          const { getSuggestionImageUrlAsync } = await import("@/lib/utils/trip-image-utils")
          const freshUrl = await getSuggestionImageUrlAsync(suggestion, placeholderSize)
          if (freshUrl && !freshUrl.includes("placeholder.svg")) {
            setImageUrl(freshUrl)
            setHasError(false)
            return
          }
        } catch (error) {
          console.error("Error getting fresh image URL on error:", error)
        }
      }

      // Fallback to regular retry
      loadImage()
    } else {
      setHasError(true)
      setImageUrl(
        `/placeholder.svg?height=${placeholderSize.split("x")[1]}&width=${placeholderSize.split("x")[0]}`
      )
    }
  }, [retryCount, loadImage, placeholderSize, suggestion])

  useEffect(() => {
    loadImage()
  }, [loadImage])

  return {
    imageUrl,
    isLoading,
    hasError,
    retryCount,
    handleImageError,
  }
}
